// Import removed to fix ESLint warning
import { useLoginStore } from "~/store/user";
export default defineNuxtRouteMiddleware((to) => {
  const nuxtApp = useNuxtApp();
  const loginStore = useLoginStore();
  const isLoggedIn = loginStore.isLoggedIn;
  const guestRoutes = [
    "/login",
    "/criar-conta",
    "/forgot-password",
    "/recover-password",
    "/completar-cadastro",
    "/super-admin-login",
  ];

  // Handle root path early
  if (to.path === "/") {
    if (isLoggedIn) return; // Allow logged-in users
    return navigateTo("/login"); // Redirect others
  }

  // Remove trailing slash EXCEPT for root
  if (to.path.endsWith("/")) {
    const newPath = to.path.replace(/\/+$/, ""); // Trim all trailing slashes
    return navigateTo(newPath || "/", { redirectCode: 301 }); // Fallback to root
  }

  // Auth checks
  if (!isLoggedIn) {
    if (!guestRoutes.includes(to.path)) {
      return navigateTo("/login");
    }
  } else {
    if (guestRoutes.includes(to.path)) {
      return navigateTo("/");
    }
  }

  if (!guestRoutes.includes(to.path) && !isLoggedIn) {
    // @ts-expect-error: nuxt internal bug
    return nuxtApp.$router.push("/login");
    // const toast = useToast()
    // toast.error('Sessão expirada')
  }
  if (guestRoutes.includes(to.path) && isLoggedIn) {
    // @ts-expect-error: nuxt internal bug
    return nuxtApp.$router.push("/");
  }
});
