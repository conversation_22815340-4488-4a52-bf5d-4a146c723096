<template>
  <div class="bg-base-200 flex items-center h-full">
    <div class="grid md:grid-cols-9 w-full h-full grid-cols-1 lg:grid-cols-2 bg-base-100 rounded-xl">
      <ClientOnly>
        <div class="pb-24 px-8 mx-auto my-auto md:my w-full max-w-lg col-span-1 md:col-span-4 lg:col-span-1">
          <div class="text-xl md:text-2xl mb-2 md:mb-4 font-semibold text-center pt-12">
            Completar Cadastro
          </div>
          <div class="font-semibold mb-3">
            Complete suas informações para finalizar o cadastro
          </div>
          <div class="text-red-500 text-center" v-if="error">
            {{ error }}
          </div>
          <div class="text-green-500 text-center" v-if="success">
            {{ success }}
          </div>
          
          <form class="flex flex-col gap-3" @submit="submit">
            <input-base 
              v-bind="name" 
              :error="errors['name']" 
              label="Nome Completo" 
              :readonly="!!initialData.name"
            />
            <input-base 
              v-bind="email" 
              :error="errors['email']" 
              label="Email" 
              :readonly="!!initialData.email"
            />
            <input-base v-bind="phone" :error="errors['phone']" label="Telefone" />
            <input-base
              :error="errors['password']"
              v-bind="password"
              label="Senha"
              type="password"
            />
            <input-base
              :error="errors['password_confirmation']"
              v-bind="password_confirmation"
              label="Confirmar Senha"
              type="password"
            />
            <textarea-base
              v-bind="bio"
              :error="errors['bio']"
              label="Biografia (opcional)"
              placeholder="Conte um pouco sobre você..."
            />
            <base-button type="submit" class="mt-2 w-full" :loading="loading">
              Finalizar Cadastro
            </base-button>
          </form>
        </div>
        <div class="hidden md:inline-block hero min-h-full md:col-span-5 lg:col-span-1 rounded-l-xl">
          <nuxt-img loading="lazy" :src="`https://psyplus.site/assets/login-image.jpg`"
            alt="Admin Dashboard" class="h-full"></nuxt-img>
        </div>
      </ClientOnly>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as yup from "yup";
import { useForm } from "vee-validate";
import { api } from "@/server/api";
import { useLoginStore } from "@/store/user";

definePageMeta({
  layout: 'unauthenticated'
});

const loading = ref(false);
const error = ref();
const success = ref();
const loginStore = useLoginStore();
const route = useRoute();

// Get initial data from query params (encrypted)
const initialData = ref({
  name: '',
  email: '',
  phone: ''
});

const validationSchema = yup.object().shape({
  name: yup.string().required("O nome é obrigatório"),
  email: yup.string().required("O email é obrigatório").email("Email inválido"),
  phone: yup.string().required("O telefone é obrigatório"),
  password: yup.string().required("A senha é obrigatória").min(6, "A senha deve ter no mínimo 6 caracteres"),
  password_confirmation: yup.string()
    .required("A confirmação de senha é obrigatória")
    .oneOf([yup.ref('password')], 'As senhas devem ser iguais'),
  bio: yup.string(),
});

const { handleSubmit, defineInputBinds, errors, setFieldValue } = useForm({
  validationSchema,
});

const name = defineInputBinds("name");
const email = defineInputBinds("email");
const phone = defineInputBinds("phone");
const password = defineInputBinds("password");
const password_confirmation = defineInputBinds("password_confirmation");
const bio = defineInputBinds("bio");

function handleFormSubmit(values: any) {
  completeRegistration(values);
}

async function completeRegistration(payload: any) {
  try {
    error.value = undefined;
    success.value = undefined;
    loading.value = true;
    
    const { data } = await api.post("/complete-registration", {
      ...payload,
      token: route.query.token
    });
    
    success.value = "Cadastro finalizado com sucesso!";
    
    // Auto login after successful registration
    loginStore.login(data.token);
    const userData = {
      email: data.email,
      image: data.image,
      name: data.name,
      phone: data.phone,
    };
    loginStore.setUser(userData);
    
    setTimeout(() => {
      navigateTo("/");
    }, 2000);
    
  } catch (err: any) {
    if (err.response?.data?.message) {
      error.value = err.response.data.message;
    } else {
      error.value = "Erro ao finalizar cadastro. Tente novamente.";
    }
  } finally {
    loading.value = false;
  }
}

const submit = handleSubmit(handleFormSubmit);

// Decode query parameters on mount
onMounted(() => {
  try {
    if (route.query.data) {
      // Decode the encrypted data from query params
      const decodedData = JSON.parse(atob(route.query.data as string));
      initialData.value = decodedData;
      
      // Set initial values
      setFieldValue('name', decodedData.name || '');
      setFieldValue('email', decodedData.email || '');
      setFieldValue('phone', decodedData.phone || '');
    }
  } catch (e) {
    console.error('Error decoding registration data:', e);
  }
});
</script>
