<template>
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Agenda</h1>
      <base-button @click="isScheduleFormOpen = true">
        Novo Agendamento
      </base-button>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <div class="mb-4">
        <h2 class="text-lg font-semibold mb-2">Agendamentos de Hoje</h2>
        <div class="text-gray-600">{{ currentDate }}</div>
      </div>

      <div v-if="loading" class="flex justify-center py-8">
        <loading />
      </div>

      <div v-else-if="appointments.length === 0" class="text-center py-8 text-gray-500">
        Nenhum agendamento para hoje
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="appointment in appointments"
          :key="appointment.id"
          class="border rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex justify-between items-start">
            <div>
              <h3 class="font-semibold">{{ appointment.patient_name }}</h3>
              <p class="text-gray-600">{{ appointment.service_name }}</p>
              <p class="text-sm text-gray-500">
                {{ appointment.time }} - {{ appointment.duration }}min
              </p>
            </div>
            <div class="flex gap-2">
              <base-button size="sm" variant="secondary" @click="editAppointment(appointment)">
                Editar
              </base-button>
              <base-button size="sm" variant="danger" @click="cancelAppointment(appointment)">
                Cancelar
              </base-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Schedule Form Dialog -->
    <base-dialog title="Novo Agendamento" v-model="isScheduleFormOpen">
      <schedule-register-form
        v-if="isScheduleFormOpen"
        @submit="handleScheduleCreate"
        @cancel="isScheduleFormOpen = false"
      />
    </base-dialog>

    <!-- Edit Appointment Dialog -->
    <base-dialog title="Editar Agendamento" v-model="isEditFormOpen">
      <schedule-register-form
        v-if="isEditFormOpen"
        :appointment="selectedAppointment"
        @submit="handleScheduleUpdate"
        @cancel="isEditFormOpen = false"
      />
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { api } from "@/server/api";
import { useToast } from "vue-toast-notification";

const toast = useToast();
const loading = ref(false);
const appointments = ref([]);
const isScheduleFormOpen = ref(false);
const isEditFormOpen = ref(false);
const selectedAppointment = ref(null);

const currentDate = computed(() => {
  return new Date().toLocaleDateString('pt-BR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});

const fetchAppointments = async () => {
  try {
    loading.value = true;
    const { data } = await api.get("/appointments/today");
    appointments.value = data;
  } catch (error) {
    console.error("Error fetching appointments:", error);
    toast.error("Erro ao carregar agendamentos");
  } finally {
    loading.value = false;
  }
};

const handleScheduleCreate = async (appointmentData: any) => {
  try {
    await api.post("/appointments", appointmentData);
    toast.success("Agendamento criado com sucesso!");
    isScheduleFormOpen.value = false;
    fetchAppointments();
  } catch (error) {
    console.error("Error creating appointment:", error);
    toast.error("Erro ao criar agendamento");
  }
};

const handleScheduleUpdate = async (appointmentData: any) => {
  try {
    await api.put(`/appointments/${selectedAppointment.value.id}`, appointmentData);
    toast.success("Agendamento atualizado com sucesso!");
    isEditFormOpen.value = false;
    selectedAppointment.value = null;
    fetchAppointments();
  } catch (error) {
    console.error("Error updating appointment:", error);
    toast.error("Erro ao atualizar agendamento");
  }
};

const editAppointment = (appointment: any) => {
  selectedAppointment.value = appointment;
  isEditFormOpen.value = true;
};

const cancelAppointment = async (appointment: any) => {
  if (confirm("Tem certeza que deseja cancelar este agendamento?")) {
    try {
      await api.delete(`/appointments/${appointment.id}`);
      toast.success("Agendamento cancelado com sucesso!");
      fetchAppointments();
    } catch (error) {
      console.error("Error canceling appointment:", error);
      toast.error("Erro ao cancelar agendamento");
    }
  }
};

onMounted(() => {
  fetchAppointments();
});
</script>
