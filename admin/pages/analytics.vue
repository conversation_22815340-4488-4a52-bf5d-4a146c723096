<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <!-- Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-base-100 rounded-lg p-4 shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Total Profissionais</p>
              <p class="text-2xl font-bold">{{ overview.total_professionals || 0 }}</p>
            </div>
            <div class="text-primary">
              <UserGroupIcon class="w-8 h-8" />
            </div>
          </div>
        </div>
        
        <div class="bg-base-100 rounded-lg p-4 shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Total Empresas</p>
              <p class="text-2xl font-bold">{{ overview.total_companies || 0 }}</p>
            </div>
            <div class="text-primary">
              <BuildingOfficeIcon class="w-8 h-8" />
            </div>
          </div>
        </div>
        
        <div class="bg-base-100 rounded-lg p-4 shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Total Usuários</p>
              <p class="text-2xl font-bold">{{ overview.total_users || 0 }}</p>
            </div>
            <div class="text-primary">
              <UsersIcon class="w-8 h-8" />
            </div>
          </div>
        </div>
        
        <div class="bg-base-100 rounded-lg p-4 shadow">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Receita Total</p>
              <p class="text-2xl font-bold">{{ toBrl(overview.total_revenue || 0) }}</p>
            </div>
            <div class="text-primary">
              <CurrencyDollarIcon class="w-8 h-8" />
            </div>
          </div>
        </div>
      </div>

      <!-- Period Filter -->
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Analytics Dashboard</h2>
        <div class="flex gap-2">
          <select-base
            v-model="selectedPeriod"
            :options="periodOptions"
            class="w-32"
          />
          <base-button @click="refreshData" :loading="loading" size="sm">
            <ArrowPathIcon class="w-4 h-4" />
          </base-button>
        </div>
      </div>

      <!-- Tabs -->
      <div class="tabs tabs-bordered mb-4">
        <a 
          v-for="tab in tabs" 
          :key="tab.key"
          class="tab"
          :class="{ 'tab-active': activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          {{ tab.label }}
        </a>
      </div>

      <!-- Tab Content -->
      <div v-if="activeTab === 'overview'" class="space-y-6">
        <!-- Today's Stats -->
        <div class="bg-base-100 rounded-lg p-4 shadow">
          <h3 class="text-lg font-semibold mb-4">Estatísticas de Hoje</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
              <p class="text-2xl font-bold text-primary">{{ overview.today?.new_users || 0 }}</p>
              <p class="text-sm text-gray-600">Novos Usuários</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-primary">{{ overview.today?.new_appointments || 0 }}</p>
              <p class="text-sm text-gray-600">Novos Agendamentos</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-primary">{{ toBrl(overview.today?.revenue || 0) }}</p>
              <p class="text-sm text-gray-600">Receita</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-primary">{{ overview.today?.active_users || 0 }}</p>
              <p class="text-sm text-gray-600">Usuários Ativos</p>
            </div>
          </div>
        </div>

        <!-- Bug Reports Summary -->
        <div class="bg-base-100 rounded-lg p-4 shadow">
          <h3 class="text-lg font-semibold mb-4">Relatórios de Bug</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
              <p class="text-2xl font-bold">{{ overview.bug_reports?.total || 0 }}</p>
              <p class="text-sm text-gray-600">Total</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-red-600">{{ overview.bug_reports?.open || 0 }}</p>
              <p class="text-sm text-gray-600">Abertos</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-yellow-600">{{ overview.bug_reports?.in_progress || 0 }}</p>
              <p class="text-sm text-gray-600">Em Progresso</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-red-800">{{ overview.bug_reports?.critical || 0 }}</p>
              <p class="text-sm text-gray-600">Críticos</p>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="activeTab === 'users'">
        <analytics-users :period="selectedPeriod" />
      </div>

      <div v-else-if="activeTab === 'appointments'">
        <analytics-appointments :period="selectedPeriod" />
      </div>

      <div v-else-if="activeTab === 'revenue'">
        <analytics-revenue :period="selectedPeriod" />
      </div>

      <div v-else-if="activeTab === 'access'">
        <analytics-access :period="selectedPeriod" />
      </div>

      <div v-else-if="activeTab === 'bugs'">
        <analytics-bugs />
      </div>

      <div v-else-if="activeTab === 'google'">
        <analytics-google :period="selectedPeriod" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  UserGroupIcon, 
  BuildingOfficeIcon, 
  UsersIcon, 
  CurrencyDollarIcon,
  ArrowPathIcon 
} from "@heroicons/vue/24/solid";
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const overview = ref({});
const activeTab = ref('overview');
const selectedPeriod = ref('week');

const periodOptions = [
  { value: 'day', label: 'Últimos 7 dias' },
  { value: 'week', label: 'Últimas 8 semanas' },
  { value: 'month', label: 'Últimos 12 meses' },
  { value: 'year', label: 'Últimos 5 anos' }
];

const tabs = [
  { key: 'overview', label: 'Visão Geral' },
  { key: 'users', label: 'Usuários' },
  { key: 'appointments', label: 'Agendamentos' },
  { key: 'revenue', label: 'Receita' },
  { key: 'access', label: 'Acesso' },
  { key: 'bugs', label: 'Bugs' },
  { key: 'google', label: 'Google Analytics' }
];

async function loadOverview() {
  try {
    loading.value = true;
    const { data } = await api.get('/super-admin/analytics/overview');
    overview.value = data;
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados de visão geral');
  } finally {
    loading.value = false;
  }
}

async function refreshData() {
  await loadOverview();
}

onMounted(() => {
  loadOverview();
});
</script>
