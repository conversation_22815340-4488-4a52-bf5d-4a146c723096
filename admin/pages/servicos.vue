<template>
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Serviços</h1>
      <base-button @click="isServiceFormOpen = true">
        Novo Serviço
      </base-button>
    </div>

    <div class="bg-white rounded-lg shadow">
      <div v-if="loading" class="flex justify-center py-8">
        <loading />
      </div>

      <div v-else-if="services.length === 0" class="text-center py-8 text-gray-500">
        Nenhum serviço cadastrado
      </div>

      <table-base v-else>
        <template #header>
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Nome
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Duração
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Preço
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ações
            </th>
          </tr>
        </template>
        <template #body>
          <tr v-for="service in services" :key="service.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ service.name }}</div>
              <div class="text-sm text-gray-500">{{ service.description }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ service.duration }}min
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              R$ {{ service.price }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                :class="service.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
              >
                {{ service.active ? 'Ativo' : 'Inativo' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex gap-2">
                <base-button size="sm" variant="secondary" @click="editService(service)">
                  Editar
                </base-button>
                <base-button 
                  size="sm" 
                  :variant="service.active ? 'danger' : 'success'"
                  @click="toggleServiceStatus(service)"
                >
                  {{ service.active ? 'Desativar' : 'Ativar' }}
                </base-button>
              </div>
            </td>
          </tr>
        </template>
      </table-base>
    </div>

    <!-- Service Form Dialog -->
    <base-dialog title="Novo Serviço" v-model="isServiceFormOpen">
      <services-register-form
        v-if="isServiceFormOpen"
        @submit="handleServiceCreate"
        @cancel="isServiceFormOpen = false"
      />
    </base-dialog>

    <!-- Edit Service Dialog -->
    <base-dialog title="Editar Serviço" v-model="isEditFormOpen">
      <services-register-form
        v-if="isEditFormOpen"
        :service="selectedService"
        @submit="handleServiceUpdate"
        @cancel="isEditFormOpen = false"
      />
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { api } from "@/server/api";
import { useToast } from "vue-toast-notification";

const toast = useToast();
const loading = ref(false);
const services = ref([]);
const isServiceFormOpen = ref(false);
const isEditFormOpen = ref(false);
const selectedService = ref(null);

const fetchServices = async () => {
  try {
    loading.value = true;
    const { data } = await api.get("/services");
    services.value = data;
  } catch (error) {
    console.error("Error fetching services:", error);
    toast.error("Erro ao carregar serviços");
  } finally {
    loading.value = false;
  }
};

const handleServiceCreate = async (serviceData: any) => {
  try {
    await api.post("/services", serviceData);
    toast.success("Serviço criado com sucesso!");
    isServiceFormOpen.value = false;
    fetchServices();
  } catch (error) {
    console.error("Error creating service:", error);
    toast.error("Erro ao criar serviço");
  }
};

const handleServiceUpdate = async (serviceData: any) => {
  try {
    await api.put(`/services/${selectedService.value.id}`, serviceData);
    toast.success("Serviço atualizado com sucesso!");
    isEditFormOpen.value = false;
    selectedService.value = null;
    fetchServices();
  } catch (error) {
    console.error("Error updating service:", error);
    toast.error("Erro ao atualizar serviço");
  }
};

const editService = (service: any) => {
  selectedService.value = service;
  isEditFormOpen.value = true;
};

const toggleServiceStatus = async (service: any) => {
  try {
    await api.put(`/services/${service.id}`, {
      ...service,
      active: !service.active
    });
    toast.success(`Serviço ${service.active ? 'desativado' : 'ativado'} com sucesso!`);
    fetchServices();
  } catch (error) {
    console.error("Error toggling service status:", error);
    toast.error("Erro ao alterar status do serviço");
  }
};

onMounted(() => {
  fetchServices();
});
</script>
