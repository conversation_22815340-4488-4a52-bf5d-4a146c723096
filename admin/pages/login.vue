<template>
  <div class="bg-base-200 flex items-center h-full">
    <div class="grid md:grid-cols-9 w-full h-full grid-cols-1 lg:grid-cols-2 bg-base-100 rounded-xl">
      <ClientOnly>
        <div class="pb-24 px-8 mx-auto my-auto md:my w-full max-w-lg col-span-1 md:col-span-4 lg:col-span-1">
          <div class="text-xl md:text-2xl mb-2 md:mb-4 font-semibold text-center pt-12">
            Psy + Admin Dashboard
          </div>
          <div class="font-semibold mb-3">
            Faça login ou
            <nuxt-link to="/criar-conta" class="text-primary underline">crie uma nova conta</nuxt-link>
            para acessar o painel administrativo
          </div>
          <div class="text-red-500 text-center" v-if="error">
            {{ error }}
          </div>
          <login-form @submit="handleFormSubmit" :loading="loading" />
          
          <div class="mt-6 text-center">
            <nuxt-link to="/super-admin-login" class="text-sm text-gray-600 hover:text-primary underline">
              Acesso Super Admin
            </nuxt-link>
          </div>
        </div>
        <div class="hidden md:inline-block hero min-h-full md:col-span-5 lg:col-span-1 rounded-l-xl">
          <nuxt-img loading="lazy" :src="`https://psyplus.site/assets/login-image.jpg`"
            alt="Admin Dashboard" class="h-full"></nuxt-img>
        </div>
      </ClientOnly>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "@/server/api";
import { useLoginStore } from "@/store/user";

definePageMeta({
  layout: 'unauthenticated'
});

const loading = ref(false);
const loginStore = useLoginStore();
const router = useRouter();
const runtimeConfig = useRuntimeConfig();
const error = ref();

async function handleFormSubmit(payload: { email: string; password: string }) {
  try {
    error.value = undefined;
    loading.value = true;
    const { data } = await api.post("/login-admin", payload);
    loginStore.login(data.token);
    const userData = {
      email: data.email,
      image: data.image,
      name: data.name,
      phone: data.phone,
    };
    loginStore.setUser(userData);
    router.push("/");
  } catch (err) {
    error.value = "email ou senha incorreto";
  } finally {
    loading.value = false;
  }
}
</script>
