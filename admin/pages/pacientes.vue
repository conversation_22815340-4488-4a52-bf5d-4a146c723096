<template>
  <div class="p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Pacientes</h1>
      <base-button @click="isPatientFormOpen = true">
        Novo Paciente
      </base-button>
    </div>

    <div class="bg-white rounded-lg shadow">
      <div class="p-4 border-b">
        <input-base
          v-model="searchTerm"
          placeholder="Buscar pacientes..."
          @input="searchPatients"
        />
      </div>

      <div v-if="loading" class="flex justify-center py-8">
        <loading />
      </div>

      <div v-else-if="filteredPatients.length === 0" class="text-center py-8 text-gray-500">
        {{ searchTerm ? 'Nenhum paciente encontrado' : 'Nenhum paciente cadastrado' }}
      </div>

      <table-base v-else>
        <template #header>
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Nome
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Email
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Telefone
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Última Consulta
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ações
            </th>
          </tr>
        </template>
        <template #body>
          <tr v-for="patient in filteredPatients" :key="patient.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="h-10 w-10 flex-shrink-0">
                  <img
                    v-if="patient.avatar"
                    :src="patient.avatar"
                    :alt="patient.name"
                    class="h-10 w-10 rounded-full"
                  />
                  <div
                    v-else
                    class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"
                  >
                    <span class="text-sm font-medium text-gray-700">
                      {{ patient.name.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">{{ patient.name }}</div>
                  <div class="text-sm text-gray-500">{{ patient.birth_date }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ patient.email }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ patient.phone }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ patient.last_appointment || 'Nunca' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                :class="patient.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
              >
                {{ patient.active ? 'Ativo' : 'Inativo' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex gap-2">
                <base-button size="sm" variant="secondary" @click="viewPatient(patient)">
                  Ver
                </base-button>
                <base-button size="sm" variant="secondary" @click="editPatient(patient)">
                  Editar
                </base-button>
                <base-button 
                  size="sm" 
                  :variant="patient.active ? 'danger' : 'success'"
                  @click="togglePatientStatus(patient)"
                >
                  {{ patient.active ? 'Desativar' : 'Ativar' }}
                </base-button>
              </div>
            </td>
          </tr>
        </template>
      </table-base>
    </div>

    <!-- Patient Form Dialog -->
    <base-dialog title="Novo Paciente" v-model="isPatientFormOpen">
      <pacients-register-form
        v-if="isPatientFormOpen"
        @submit="handlePatientCreate"
        @cancel="isPatientFormOpen = false"
      />
    </base-dialog>

    <!-- Edit Patient Dialog -->
    <base-dialog title="Editar Paciente" v-model="isEditFormOpen">
      <pacients-register-form
        v-if="isEditFormOpen"
        :patient="selectedPatient"
        @submit="handlePatientUpdate"
        @cancel="isEditFormOpen = false"
      />
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { api } from "@/server/api";
import { useToast } from "vue-toast-notification";

const toast = useToast();
const loading = ref(false);
const patients = ref([]);
const searchTerm = ref('');
const isPatientFormOpen = ref(false);
const isEditFormOpen = ref(false);
const selectedPatient = ref(null);

const filteredPatients = computed(() => {
  if (!searchTerm.value) return patients.value;
  
  return patients.value.filter(patient => 
    patient.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    patient.email.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    patient.phone.includes(searchTerm.value)
  );
});

const fetchPatients = async () => {
  try {
    loading.value = true;
    const { data } = await api.get("/patients");
    patients.value = data;
  } catch (error) {
    console.error("Error fetching patients:", error);
    toast.error("Erro ao carregar pacientes");
  } finally {
    loading.value = false;
  }
};

const searchPatients = () => {
  // Search is handled by computed property
};

const handlePatientCreate = async (patientData: any) => {
  try {
    await api.post("/patients", patientData);
    toast.success("Paciente criado com sucesso!");
    isPatientFormOpen.value = false;
    fetchPatients();
  } catch (error) {
    console.error("Error creating patient:", error);
    toast.error("Erro ao criar paciente");
  }
};

const handlePatientUpdate = async (patientData: any) => {
  try {
    await api.put(`/patients/${selectedPatient.value.id}`, patientData);
    toast.success("Paciente atualizado com sucesso!");
    isEditFormOpen.value = false;
    selectedPatient.value = null;
    fetchPatients();
  } catch (error) {
    console.error("Error updating patient:", error);
    toast.error("Erro ao atualizar paciente");
  }
};

const viewPatient = (patient: any) => {
  // Navigate to patient detail page
  navigateTo(`/pacientes/${patient.id}`);
};

const editPatient = (patient: any) => {
  selectedPatient.value = patient;
  isEditFormOpen.value = true;
};

const togglePatientStatus = async (patient: any) => {
  try {
    await api.put(`/patients/${patient.id}`, {
      ...patient,
      active: !patient.active
    });
    toast.success(`Paciente ${patient.active ? 'desativado' : 'ativado'} com sucesso!`);
    fetchPatients();
  } catch (error) {
    console.error("Error toggling patient status:", error);
    toast.error("Erro ao alterar status do paciente");
  }
};

onMounted(() => {
  fetchPatients();
});
</script>
