<template>
  <!-- form que cadastra e edita usuarios -->
    <form @submit.prevent="onSubmit" class="sm:pb-14 p-4">
      <div class="flex items-end gap-2 mb-2">
        <input-base type="text" label="Telefone *" v-model="phone" data-maska="(##) #####-####" :error="errors.phone" />
        <base-button @click="searchUser" size="sm" type="button" class="!h-[39px] !w-[39px] min-h-0">
          <MagnifyingGlassIcon class="w-5" />
        </base-button>
      </div>

      <div v-if="userSearched" class="space-y-4">

        <input-base type="text" label="Nome *" v-model="name" :readonly="userId" :error="errors.name" />
        <input-base type="email" label="Email" v-model="email" :readonly="userId" :error="errors.email" />
        <!-- <input-base v-if="userId && birthday || !userId" type="date" label="Data de Nascimento" v-model="birthday"
          :readonly="userId" :error="errors.birthday" /> -->
      </div>
      <div class="fixed bottom-2 md:bottom-4 left-0 px-4 md:px-10 w-full">
        <base-button v-if="userSearched" type="submit" class="w-full mt-4" :loading="loading">
          {{ userId ? 'Vincular Paciente' : 'Cadastrar Paciente' }}
        </base-button>
      </div>
    </form>
    <!-- <div class="fixed bottom-2 md:bottom-4 left-0 px-4 md:px-10 w-full">
      <base-button :loading="loading" class="w-full mt-4 mb-2">Salvar</base-button>
    </div> -->
</template>

<script setup lang="ts">
import { MagnifyingGlassIcon } from "@heroicons/vue/24/solid";
// import { useToast } from "vue-toast-notification";
import { useForm } from "vee-validate";
import * as yup from "yup";
import { api } from "~/server/api";
const loading = ref()
const schema = yup.object({
  phone: yup.string().required("Telefone é obrigatório"),
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido"),
  birthday: yup.string().nullable()
});
const { handleSubmit, defineField, errors } = useForm({
  validationSchema: schema
});
const userSearched = ref(false);
const [phone] = defineField('phone');
const [name] = defineField('name');
const [email] = defineField('email');
// const [birthday] = defineField('birthday');
const onSubmit = handleSubmit(async (values) => {
  try {
    loading.value = true;
    if (userId.value) {
      await api.post('/vinculate-client', { ...values, user_id: userId.value });
    } else {
      await api.post("/create-client", values);
    }

    toast.success(userId.value ? "Paciente vinculado com sucesso!" : "Paciente cadastrado com sucesso!");
    isOpened.value = false;
    resetFormValues();
    await getData();

  } catch (err) {
    console.error(err);
    toast.error(userId.value ? "Erro ao vincular paciente!" : "Erro ao cadastrar pacientes!");
  } finally {
    loading.value = false;
  }
});
async function searchUser() {
  if (!phone.value) return;
  try {
    loading.value = true;
    const { data } = await api.get("/search-user", {
      params: { phone: phone.value },
    });
    userSearched.value = true;

    if (data) {
      name.value = data.name;
      email.value = data.email;
      userId.value = data.id;
    } else {
      name.value = "";
      email.value = "";
      userId.value = null;
    }
  } catch (err) {
    console.error(err);
    toast.error("Erro ao buscar usuário");
  } finally {
    loading.value = false;
  }
}

</script>
```
