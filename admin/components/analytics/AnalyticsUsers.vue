<template>
  <div class="space-y-6">
    <!-- User Registration Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Registros de Usuários ao Longo do Tempo</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="userAnalytics.registrations_over_time?.length" class="h-64">
        <canvas ref="registrationChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- User Types Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Tipos de Usuário</h3>
        <div v-if="userAnalytics.user_types_breakdown?.length" class="space-y-2">
          <div 
            v-for="type in userAnalytics.user_types_breakdown" 
            :key="type.type"
            class="flex justify-between items-center p-2 bg-base-200 rounded"
          >
            <span class="capitalize">{{ getUserTypeLabel(type.type) }}</span>
            <span class="font-semibold">{{ type.count }}</span>
          </div>
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>

      <!-- User Retention -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Retenção de Usuários</h3>
        <div v-if="userAnalytics.user_retention" class="space-y-4">
          <div class="text-center">
            <p class="text-3xl font-bold text-primary">
              {{ userAnalytics.user_retention.retention_rate }}%
            </p>
            <p class="text-sm text-gray-600">Taxa de Retenção (30 dias)</p>
          </div>
          <div class="grid grid-cols-2 gap-4 text-center">
            <div>
              <p class="text-xl font-semibold">{{ userAnalytics.user_retention.total_users }}</p>
              <p class="text-xs text-gray-600">Total de Usuários</p>
            </div>
            <div>
              <p class="text-xl font-semibold">{{ userAnalytics.user_retention.retained_users }}</p>
              <p class="text-xs text-gray-600">Usuários Retidos</p>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>
    </div>

    <!-- Active Users Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Usuários Ativos ao Longo do Tempo</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="userAnalytics.active_users_over_time?.length" class="h-64">
        <canvas ref="activeUsersChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

const props = defineProps({
  period: {
    type: String,
    default: 'week'
  }
});

const toast = useToast();
const loading = ref(false);
const userAnalytics = ref({});
const registrationChart = ref(null);
const activeUsersChart = ref(null);
let registrationChartInstance = null;
let activeUsersChartInstance = null;

function getUserTypeLabel(type: string) {
  const labels = {
    'user': 'Pacientes',
    'admin': 'Profissionais',
    'super_admin': 'Super Admins'
  };
  return labels[type] || type;
}

async function loadUserAnalytics() {
  try {
    loading.value = true;
    const { data } = await api.get('/super-admin/analytics/users', {
      params: { period: props.period }
    });
    userAnalytics.value = data;
    
    nextTick(() => {
      createCharts();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar analytics de usuários');
  } finally {
    loading.value = false;
  }
}

function createCharts() {
  createRegistrationChart();
  createActiveUsersChart();
}

function createRegistrationChart() {
  if (registrationChartInstance) {
    registrationChartInstance.destroy();
  }

  if (!registrationChart.value || !userAnalytics.value.registrations_over_time?.length) return;

  const ctx = registrationChart.value.getContext('2d');
  const data = userAnalytics.value.registrations_over_time;

  registrationChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: data.map(item => item.period),
      datasets: [{
        label: 'Novos Registros',
        data: data.map(item => item.count),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.1,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

function createActiveUsersChart() {
  if (activeUsersChartInstance) {
    activeUsersChartInstance.destroy();
  }

  if (!activeUsersChart.value || !userAnalytics.value.active_users_over_time?.length) return;

  const ctx = activeUsersChart.value.getContext('2d');
  const data = userAnalytics.value.active_users_over_time;

  activeUsersChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map(item => item.period),
      datasets: [{
        label: 'Usuários Ativos',
        data: data.map(item => item.count),
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

watch(() => props.period, () => {
  loadUserAnalytics();
});

onMounted(() => {
  loadUserAnalytics();
});

onUnmounted(() => {
  if (registrationChartInstance) {
    registrationChartInstance.destroy();
  }
  if (activeUsersChartInstance) {
    activeUsersChartInstance.destroy();
  }
});
</script>
