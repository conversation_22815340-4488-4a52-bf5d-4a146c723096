<template>
  <div class="space-y-6">
    <!-- Configuration Status -->
    <div v-if="!googleAnalytics.is_configured" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <ExclamationTriangleIcon class="w-5 h-5 text-yellow-600 mr-2" />
        <p class="text-yellow-800">
          Google Analytics não está configurado. Configure as credenciais para ver os dados reais.
        </p>
      </div>
    </div>

    <!-- Basic Analytics -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Métricas Básicas do Google Analytics</h3>
      <div v-if="googleAnalytics.basic_analytics" class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ googleAnalytics.basic_analytics.active_users || 0 }}
          </p>
          <p class="text-sm text-gray-600">Usuários Ativos</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ googleAnalytics.basic_analytics.sessions || 0 }}
          </p>
          <p class="text-sm text-gray-600">Sessões</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ googleAnalytics.basic_analytics.page_views || 0 }}
          </p>
          <p class="text-sm text-gray-600">Visualizações</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ googleAnalytics.basic_analytics.bounce_rate || 0 }}%
          </p>
          <p class="text-sm text-gray-600">Taxa de Rejeição</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ Math.round(googleAnalytics.basic_analytics.avg_session_duration || 0) }}s
          </p>
          <p class="text-sm text-gray-600">Duração Média</p>
        </div>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Page Views Over Time Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Visualizações ao Longo do Tempo</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="googleAnalytics.page_views_over_time?.length" class="h-64">
        <canvas ref="pageViewsChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Demographics and Top Pages -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- User Demographics -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Demografia dos Usuários</h3>
        <div v-if="googleAnalytics.user_demographics">
          <!-- Countries -->
          <div class="mb-4">
            <h4 class="font-medium mb-2">Por País</h4>
            <div v-if="Object.keys(googleAnalytics.user_demographics.countries || {}).length" class="space-y-2">
              <div 
                v-for="(count, country) in googleAnalytics.user_demographics.countries" 
                :key="country"
                class="flex justify-between items-center p-2 bg-base-200 rounded"
              >
                <span>{{ country }}</span>
                <span class="font-semibold">{{ count }}</span>
              </div>
            </div>
            <div v-else class="text-center py-2 text-gray-500 text-sm">
              Nenhum dado disponível
            </div>
          </div>

          <!-- Devices -->
          <div>
            <h4 class="font-medium mb-2">Por Dispositivo</h4>
            <div v-if="Object.keys(googleAnalytics.user_demographics.devices || {}).length" class="space-y-2">
              <div 
                v-for="(count, device) in googleAnalytics.user_demographics.devices" 
                :key="device"
                class="flex justify-between items-center p-2 bg-base-200 rounded"
              >
                <span class="capitalize">{{ device }}</span>
                <span class="font-semibold">{{ count }}</span>
              </div>
            </div>
            <div v-else class="text-center py-2 text-gray-500 text-sm">
              Nenhum dado disponível
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>

      <!-- Top Pages -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Páginas Mais Visitadas</h3>
        <div v-if="googleAnalytics.top_pages?.length" class="space-y-2">
          <div 
            v-for="page in googleAnalytics.top_pages" 
            :key="page.path"
            class="p-2 bg-base-200 rounded"
          >
            <div class="flex justify-between items-center">
              <span class="font-medium">{{ page.title || page.path }}</span>
              <span class="text-sm font-semibold">{{ page.page_views }}</span>
            </div>
            <div class="text-xs text-gray-600 mt-1">
              {{ page.path }} • {{ page.active_users }} usuários únicos
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>
    </div>

    <!-- Configuration Instructions -->
    <div v-if="!googleAnalytics.is_configured" class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Como Configurar o Google Analytics</h3>
      <div class="prose max-w-none">
        <ol class="list-decimal list-inside space-y-2">
          <li>Crie um projeto no Google Cloud Console</li>
          <li>Ative a Google Analytics Reporting API</li>
          <li>Crie uma conta de serviço e baixe o arquivo JSON de credenciais</li>
          <li>Configure as variáveis de ambiente:
            <ul class="list-disc list-inside ml-4 mt-2">
              <li><code class="bg-gray-100 px-1 rounded">GOOGLE_ANALYTICS_PROPERTY_ID</code></li>
              <li><code class="bg-gray-100 px-1 rounded">GOOGLE_ANALYTICS_CREDENTIALS_PATH</code></li>
              <li><code class="bg-gray-100 px-1 rounded">GOOGLE_ANALYTICS_MEASUREMENT_ID</code></li>
            </ul>
          </li>
          <li>Adicione a conta de serviço como usuário no Google Analytics</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ExclamationTriangleIcon } from "@heroicons/vue/24/solid";
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

const props = defineProps({
  period: {
    type: String,
    default: 'week'
  }
});

const toast = useToast();
const loading = ref(false);
const googleAnalytics = ref({});
const pageViewsChart = ref(null);
let pageViewsChartInstance = null;

async function loadGoogleAnalytics() {
  try {
    loading.value = true;
    const { data } = await api.get('/super-admin/analytics/google', {
      params: { period: props.period }
    });
    googleAnalytics.value = data;
    
    nextTick(() => {
      createChart();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados do Google Analytics');
  } finally {
    loading.value = false;
  }
}

function createChart() {
  if (pageViewsChartInstance) {
    pageViewsChartInstance.destroy();
  }

  if (!pageViewsChart.value || !googleAnalytics.value.page_views_over_time?.length) return;

  const ctx = pageViewsChart.value.getContext('2d');
  const data = googleAnalytics.value.page_views_over_time;

  pageViewsChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: data.map(item => item.date),
      datasets: [
        {
          label: 'Visualizações de Página',
          data: data.map(item => item.page_views),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.1,
          fill: true
        },
        {
          label: 'Usuários Ativos',
          data: data.map(item => item.active_users),
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          tension: 0.1,
          fill: false
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

watch(() => props.period, () => {
  loadGoogleAnalytics();
});

onMounted(() => {
  loadGoogleAnalytics();
});

onUnmounted(() => {
  if (pageViewsChartInstance) {
    pageViewsChartInstance.destroy();
  }
});
</script>
