<template>
  <div class="btm-nav flex">
    <button
      v-for="({ icon, to, label }, i) in navigationBottom.routes"
      :key="i"
      :class="to === route.path ? 'active-navigation' : 'bg-base-200'"
      class="relative px-1"
      @click="router.push(to)"
    >
      <component
        :class="to === route.path ? 'fill-primary-content' : ''"
        :is="icon"
        class="w-5 h-5"
      ></component>
      <span class="text-xs">{{ label }}</span>
    </button>
  </div>
</template>
<script setup lang="ts">
import { useNavigationBottom } from "@/store/navigationBottom";
const navigationBottom = useNavigationBottom();
const router = useRouter();
const route = useRoute();
</script>
<style lang="scss">
.active-navigation {
  @apply active bg-primary border-primary-content text-primary-content;
}
</style>
