<template>
  <div :class="isLogin ? 'h-screen' : 'h-sm'" class="drawer lg:h-screen">
    <input id="my-drawer" type="checkbox" class="drawer-toggle" v-model="drawer.active" />
    <div class="bg-base-200 drawer-content flex h-full overflow-auto">
      <slot></slot>
    </div>
    <div class="drawer-side z-50 shadow-xl">
      <label for="my-drawer" class="drawer-overlay z-50"></label>
      <ul class="bg-base-100 menu p-0 text-base gap-0 w-[310px] h-full z-50 relative">
        <XMarkIcon @click="drawer.active = false" class="w-6 right-2 absolute top-2" />
        <div class="flex items-center pl-2 pt-2 pb-1">
          <nuxt-img loading="lazy" class="-mt-1 ml-1" :src="`${runtime.public.API_URL}/icons/logo.png`" width="35" />
          <!-- <nuxt-img loading="lazy"
            :src="`${runtime.public.API_URL}/icons/logo/logo-text${isLight ? '' : '-branco'}.png`" width="105" /> -->
          <div class="text-3xl font-bold ml-2 -mt-1 text-primary">
            Psy +
          </div>
        </div>
        <li class="my-1" v-for="{ to, label, icon, items, ref } in routes" :key="label">
          <details :ref="ref" v-if="items" :open="open">
            <summary class="font-semibold">
              <component :is="icon" class="w-5 h-5 mt-[2px]"></component>
              <span>
                {{ label }}
              </span>
            </summary>
            <ul>
              <li class="font-semibold cursor-pointer" :class="item.to === route.path
                ? 'bg-primary hover:bg-primary !text-white rounded-r-full border-r-2 border-primary'
                : ''
                " v-for="(item, i) in items" :key="i">
                <nuxt-link @click="drawer.active = false" :class="item.to === route.path ? '!text-white' : ''"
                  :to="item.to">{{ item.label }}</nuxt-link>
              </li>
            </ul>
          </details>
          <div v-else @click="
            () => {
              router.push(`${to}`);
              drawer.active = false;
            }
          " class="flex font-semibold items-center py-2 pr-1 cursor-pointer relative" :class="[
            to === route.path
              ? 'bg-primary hover:bg-primary !text-white rounded-r-full border-r-2 border-primary'
              : '',
          ]">
            <custom-icon v-if="typeof icon === 'string'" :url="icon" class="w-5 h-5"></custom-icon>
            <component v-else :is="icon" class="w-5 h-5"></component>
            <a>{{ label }}</a>
          </div>
        </li>
        <li class="my-1">
          <nuxt-link to="/perfil" @click="drawer.active = false"
            :class="route.path === '/perfil' ? 'bg-primary hover:bg-primary !text-white rounded-r-full border-r-2 border-primary' : 'flex font-semibold items-center py-2 pr-1 cursor-pointer relative'">
            <UserCircleIcon class="w-5 h-5"></UserCircleIcon>
            <a>Meus dados</a>
          </nuxt-link>
        </li>
        <li class="my-1">
          <div @click="logout()" class="flex font-semibold items-center py-2 pr-1 cursor-pointer relative">
            <ArrowLeftOnRectangleIcon class="w-5 h-5"></ArrowLeftOnRectangleIcon>
            <a>Sair</a>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useDrawer } from "@/store/drawer";
import { useLoginStore } from "@/store/user";
import {
  ArrowLeftOnRectangleIcon,
  UserCircleIcon,
  XMarkIcon,
} from "@heroicons/vue/24/solid";
const router = useRouter();
const route = useRoute();
const drawer = useDrawer();
const user = useLoginStore();
const financasRef = ref();
// const appRef = ref();
const stockRef = ref();
const open = ref(false);
const runtime = useRuntimeConfig();
const colorMode = useColorMode();
const isLight = computed(() => colorMode.preference === "light");
const isLogin = computed(() => {
  return [
    "/login/",
    "/forgot-password/",
    "/criar-conta/",
    "/recover-password/",
    "/completar-cadastro/",
  ].includes(route.path);
});
function logout() {
  user.logout();
  drawer.active = false;
  router.push("/login");
}
function handleToggleOption() {
  const financeiro = ["contas", "relatorio"];
  // const app = ["cores", "landing-page"];
  const stock = ["produtos", "movimentacao-produtos"];
  const routeName = route.path.split("/")[1];
  if (financeiro.includes(routeName)) {
    if (financasRef.value) financasRef.value[0].open = true;
  }
  // if (app.includes(routeName)) {
  //   appRef.value[0].open = true;
  // }
  if (stock.includes(routeName)) {
    if (stockRef.value) stockRef.value[0].open = true;
  }
}
import {
  CalendarDaysIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ArrowLeftOnRectangleIcon,
} from "@heroicons/vue/24/solid";
import {
  ClipboardDocumentListIcon,
  ClipboardIcon,
  ChatBubbleLeftIcon,
  Cog8ToothIcon,
  ClockIcon,
} from "@heroicons/vue/24/outline";
export type Routes = {
  to?: string;
  label: string;
  icon: typeof CurrencyDollarIcon | string;
  classes?: string;
  items?: {
    label: string;
    to: string;
  }[];
  ref?: string;
};
const routes = computed(() => {
  const routes: Routes[] = [
    {
      to: "/servicos",
      label: "Serviços",
      icon: "/icons/psi.svg",
      classes: "w-6 h-6",
    },
    {
      to: "/agenda",
      label: "Agenda",
      icon: CalendarDaysIcon,
    },
    {
      to: "/orders",
      label: "Agendamentos",
      icon: ClipboardDocumentListIcon,
    },
    // {
    //   to: "/profissionais",
    //   label: "Profissionais",
    //   icon: BriefcaseIcon,
    // },
    {
      to: "/pacientes",
      label: "Pacientes",
      icon: UserGroupIcon,
    },
    {
      to: "/perguntas",
      label: "Formulário de triagem",
      icon: ChatBubbleLeftIcon,
    },
    {
      to: "/anotacoes",
      label: "Evolução do paciente",
      icon: ClipboardIcon,
    },
    {
      to: "/horarios",
      label: "Horários",
      icon: ClockIcon,
    },
    {
      to: "/config",
      label: "Configurações",
      icon: Cog8ToothIcon,
    },
    // {
    //   to: "/cores",
    //   label: "Meu aplicativo",
    //   ref: "appRef",
    //   icon: DevicePhoneMobileIcon,
    //   // items: [
    //   //   {
    //   //     label: "Cores",
    //   //     to: "/cores",
    //   //   },
    //   //   {
    //   //     label: "Pagina inicial",
    //   //     to: "/landing-page",
    //   //   },
    //   // ],
    // },
    // {
    //   label: "Finanças",
    //   icon: CurrencyDollarIcon,
    //   ref: "financasRef",

    //   items: [
    //     {
    //       label: "Contas a pagar",
    //       to: "/contas",
    //     },
    //     {
    //       label: "Relatórios",
    //       to: "/relatorio",
    //     },
    //     {
    //       label: "Movimentações",
    //       to: "/movimentacoes",
    //     },
    //   ],
    // },
    // {
    //   to: "",
    //   label: "Estoque",
    //   icon: ShoppingBagIcon,
    // },
  ];
  // if (user.isWorker) {
  //   return routes.filter((route) => !routesToRestrict.includes(route.label));
  // }
  return routes;
});
onMounted(() => {
  handleToggleOption();
});
</script>
