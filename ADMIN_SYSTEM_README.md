# Admin Analytics System

This document describes the new admin analytics system that has been implemented for the PsyPlus platform.

## Overview

The system consists of three main parts:
- **@dash/**: Frontend for professionals (psychologists) to manage their users
- **@back/**: Laravel backend with Vue.js frontend for patients to find and book appointments
- **@admin/**: New admin dashboard for system administrators with analytics and reporting

## New Admin System Features

### 1. Analytics Dashboard
- **Overview**: Total professionals, companies, users, revenue, and daily statistics
- **User Analytics**: Registration trends, user types breakdown, retention rates
- **Appointment Analytics**: Booking trends, completion rates, top services
- **Revenue Analytics**: Revenue over time, subscription analytics, average order value
- **Access Analytics**: Daily access by user type, page views, session statistics
- **Bug Reports**: Bug tracking and management system
- **Google Analytics**: Integration with Google Analytics for web analytics

### 2. Bug Reporting System
- Users can report bugs with severity levels (low, medium, high, critical)
- Admins can assign, track, and resolve bug reports
- Categories and status tracking
- File attachments support

### 3. Google Analytics Integration
- Automatic tracking for both @dash/ and @back/ systems
- Real-time analytics data in admin dashboard
- Page views, user demographics, device breakdown
- Custom event tracking

## Backend Implementation

### New Models
- `Analytics`: Tracks user events and interactions
- `AccessLog`: Detailed access logging for all requests
- `BugReport`: Bug report management
- `SystemMetric`: Daily system metrics storage
- `SubscriptionAnalytics`: Subscription and payment tracking

### New Controllers
- `AdminAnalyticsController`: Main analytics API endpoints
- `BugReportController`: Bug report management
- `SuperAdminAuthController`: Super admin authentication

### New Middleware
- `SuperAdminMiddleware`: Restricts access to super admin users
- `AnalyticsMiddleware`: Automatic analytics tracking

### New Commands
- `GenerateDailyMetrics`: Daily command to generate system metrics

## Frontend Implementation (@admin/)

### Main Components
- `AnalyticsUsers.vue`: User analytics with charts
- `AnalyticsAppointments.vue`: Appointment analytics
- `AnalyticsRevenue.vue`: Revenue analytics
- `AnalyticsAccess.vue`: Access analytics
- `AnalyticsBugs.vue`: Bug report management
- `AnalyticsGoogle.vue`: Google Analytics integration

### Authentication
- Super admin login page at `/super-admin-login`
- Token-based authentication
- Automatic redirection for authenticated users

## Setup Instructions

### 1. Database Migration
```bash
cd back
php artisan migrate
```

### 2. Create Super Admin User
```bash
php artisan db:seed --class=SuperAdminSeeder
```

Default credentials:
- Email: `<EMAIL>`
- Password: `admin123`

**Important**: Change the password after first login!

### 3. Environment Configuration

Add to your `.env` file:
```env
# Google Analytics Configuration
GOOGLE_ANALYTICS_PROPERTY_ID=your_property_id
GOOGLE_ANALYTICS_CREDENTIALS_PATH=/path/to/credentials.json
GOOGLE_ANALYTICS_MEASUREMENT_ID=G-XXXXXXXXXX
```

### 4. Google Analytics Setup (Optional)

1. Create a Google Cloud Project
2. Enable Google Analytics Reporting API
3. Create a service account and download credentials JSON
4. Add the service account to your Google Analytics property
5. Configure environment variables

### 5. Install Dependencies

For the admin frontend:
```bash
cd admin
npm install
```

### 6. Start the Admin System
```bash
cd admin
npm run dev
```

The admin system will be available at `http://localhost:3000`

## API Endpoints

### Authentication
- `POST /api/login-super-admin` - Super admin login
- `POST /api/super-admin/logout` - Logout
- `GET /api/super-admin/user` - Get authenticated user

### Analytics
- `GET /api/super-admin/analytics/overview` - Dashboard overview
- `GET /api/super-admin/analytics/users` - User analytics
- `GET /api/super-admin/analytics/appointments` - Appointment analytics
- `GET /api/super-admin/analytics/revenue` - Revenue analytics
- `GET /api/super-admin/analytics/access` - Access analytics
- `GET /api/super-admin/analytics/bugs` - Bug analytics
- `GET /api/super-admin/analytics/google` - Google Analytics data

### Bug Reports
- `GET /api/super-admin/bug-reports` - List bug reports
- `POST /api/bug-reports` - Create bug report (all authenticated users)
- `PUT /api/super-admin/bug-reports/{id}` - Update bug report
- `DELETE /api/super-admin/bug-reports/{id}` - Delete bug report
- `POST /api/super-admin/bug-reports/{id}/resolve` - Mark as resolved

## Daily Metrics Generation

The system automatically generates daily metrics at 1:00 AM. You can also run it manually:

```bash
php artisan metrics:generate-daily
```

To generate metrics for a specific date:
```bash
php artisan metrics:generate-daily 2024-01-15
```

## Security Considerations

1. **Super Admin Access**: Only users with `type = 'super_admin'` can access the admin system
2. **Token Authentication**: Uses Laravel Sanctum for API authentication
3. **Middleware Protection**: All admin routes are protected by super admin middleware
4. **Data Sanitization**: Sensitive data is automatically redacted in logs

## Monitoring and Alerts

The system tracks:
- Daily active users
- System performance metrics
- Error rates and bug reports
- Revenue and subscription metrics
- User engagement analytics

## Troubleshooting

### Common Issues

1. **Google Analytics not working**
   - Check if credentials are properly configured
   - Verify service account has access to GA property
   - Check console for JavaScript errors

2. **Charts not displaying**
   - Ensure Chart.js is properly loaded
   - Check browser console for errors
   - Verify API endpoints are returning data

3. **Authentication issues**
   - Verify super admin user exists in database
   - Check token expiration
   - Ensure middleware is properly registered

### Logs

Check Laravel logs for backend issues:
```bash
tail -f back/storage/logs/laravel.log
```

## Future Enhancements

Planned features:
- Real-time notifications for critical bugs
- Advanced filtering and search capabilities
- Export functionality for reports
- Email alerts for system metrics
- Mobile app for admin access
- Integration with external monitoring tools

## Support

For technical support or questions about the admin system, please contact the development team.
